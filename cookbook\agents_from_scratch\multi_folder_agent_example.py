"""Example usage of the Multi-Folder Agent

This script demonstrates various ways to use the MultiFolderAgent:
1. Basic initialization with multiple folders
2. Dynamic file and folder attachment
3. Interactive querying
4. Batch processing of documents

Install dependencies: `pip install openai lancedb tantivy agno`
"""

import os
from pathlib import Path
from multi_folder_agent import MultiFolderAgent, interactive_mode


def create_sample_documents():
    """Create sample documents for testing (optional)"""
    sample_dir = Path("sample_docs")
    sample_dir.mkdir(exist_ok=True)
    
    # Create sample text files
    (sample_dir / "sample1.txt").write_text("""
    This is a sample document about artificial intelligence.
    AI has revolutionized many industries including healthcare, finance, and transportation.
    Machine learning algorithms can process vast amounts of data to identify patterns.
    """)
    
    (sample_dir / "sample2.txt").write_text("""
    Python is a versatile programming language used in data science.
    It has extensive libraries like pandas, numpy, and scikit-learn.
    Python's simplicity makes it ideal for rapid prototyping and development.
    """)
    
    # Create sample JSON file
    import json
    sample_data = {
        "project": "Multi-Folder Agent",
        "description": "An AI agent that can work with multiple document sources",
        "features": ["Multi-folder support", "Dynamic attachment", "Various file formats"],
        "technologies": ["Python", "OpenAI", "LanceDB", "Agno"]
    }
    
    with open(sample_dir / "project_info.json", "w") as f:
        json.dump(sample_data, f, indent=2)
    
    print(f"✅ Sample documents created in {sample_dir}")
    return sample_dir


def example_basic_usage():
    """Example 1: Basic usage with predefined folders"""
    print("\n" + "="*60)
    print("EXAMPLE 1: Basic Usage with Predefined Folders")
    print("="*60)
    
    # Create sample documents if they don't exist
    sample_dir = create_sample_documents()
    
    # Initialize agent with sample folder
    agent = MultiFolderAgent(
        name="Document Research Assistant",
        initial_folders=[sample_dir],
        model_id="gpt-4o"
    )
    
    # Load the knowledge base
    print("🔄 Loading knowledge base...")
    agent.load_initial_knowledge(recreate=True)
    
    # Test queries
    print("\n📝 Testing basic queries...")
    
    queries = [
        "What documents do you have access to?",
        "Tell me about artificial intelligence from your documents.",
        "What programming languages are mentioned in your knowledge base?",
    ]
    
    for query in queries:
        print(f"\n❓ Query: {query}")
        print("🤖 Response:")
        agent.print_response(query, stream=False)
        print("-" * 40)


def example_dynamic_attachment():
    """Example 2: Dynamic file and folder attachment"""
    print("\n" + "="*60)
    print("EXAMPLE 2: Dynamic File and Folder Attachment")
    print("="*60)
    
    # Initialize agent with no initial folders
    agent = MultiFolderAgent(
        name="Dynamic Document Agent",
        initial_folders=[],
    )
    
    # Create additional sample documents
    dynamic_dir = Path("dynamic_docs")
    dynamic_dir.mkdir(exist_ok=True)
    
    # Create a research document
    (dynamic_dir / "research_notes.txt").write_text("""
    Research Notes on Large Language Models:
    
    1. Transformer Architecture: The foundation of modern LLMs
    2. Training Process: Pre-training on large text corpora
    3. Fine-tuning: Adapting models for specific tasks
    4. Applications: Text generation, translation, summarization
    5. Challenges: Hallucination, bias, computational requirements
    """)
    
    # Create a meeting notes document
    (dynamic_dir / "meeting_notes.txt").write_text("""
    Team Meeting Notes - AI Project Discussion:
    
    Date: Today
    Attendees: Development Team
    
    Agenda:
    - Review current AI agent capabilities
    - Discuss multi-folder document processing
    - Plan next development phases
    
    Action Items:
    - Implement dynamic file attachment
    - Test with various document formats
    - Prepare user documentation
    """)
    
    print("📁 Dynamically attaching folder...")
    success = agent.attach_folder(dynamic_dir, metadata={"category": "research", "priority": "high"})
    
    if success:
        print("✅ Folder attached successfully!")
        
        # Test queries on dynamically attached content
        queries = [
            "What research notes do you have?",
            "Summarize the meeting notes.",
            "What are the main challenges with large language models?",
        ]
        
        for query in queries:
            print(f"\n❓ Query: {query}")
            print("🤖 Response:")
            agent.print_response(query, stream=False)
            print("-" * 40)
    
    # Example of attaching individual files
    print("\n📄 Attaching individual file...")
    individual_file = dynamic_dir / "additional_info.txt"
    individual_file.write_text("""
    Additional Information:
    This document was attached individually to demonstrate single file attachment capability.
    The multi-folder agent supports various file formats including PDF, DOCX, TXT, JSON, and CSV.
    """)
    
    agent.attach_file(individual_file, metadata={"type": "documentation", "source": "manual"})


def example_interactive_session():
    """Example 3: Interactive session"""
    print("\n" + "="*60)
    print("EXAMPLE 3: Interactive Session")
    print("="*60)
    
    # Create sample documents
    sample_dir = create_sample_documents()
    
    # Initialize agent
    agent = MultiFolderAgent(
        name="Interactive Document Assistant",
        initial_folders=[sample_dir],
    )
    
    # Load knowledge base
    agent.load_initial_knowledge(recreate=False)
    
    print("🚀 Starting interactive mode...")
    print("You can now interact with the agent using natural language!")
    print("Try commands like:")
    print("  - 'What documents do you have?'")
    print("  - 'attach folder /path/to/folder'")
    print("  - 'stats'")
    print("  - 'quit' to exit")
    
    # Start interactive mode
    interactive_mode(agent)


def example_batch_processing():
    """Example 4: Batch processing multiple queries"""
    print("\n" + "="*60)
    print("EXAMPLE 4: Batch Processing Multiple Queries")
    print("="*60)
    
    # Create sample documents
    sample_dir = create_sample_documents()
    
    # Initialize agent
    agent = MultiFolderAgent(
        name="Batch Processing Agent",
        initial_folders=[sample_dir],
    )
    
    # Load knowledge base
    agent.load_initial_knowledge(recreate=False)
    
    # Batch queries
    batch_queries = [
        "List all the technologies mentioned in your documents.",
        "What are the main topics covered in your knowledge base?",
        "Provide a summary of each document you have access to.",
        "What programming concepts are discussed?",
        "Are there any project-related information in your documents?",
    ]
    
    print("🔄 Processing batch queries...")
    results = []
    
    for i, query in enumerate(batch_queries, 1):
        print(f"\n📝 Query {i}/{len(batch_queries)}: {query}")
        print("🤖 Response:")
        response = agent.run(query, stream=False)
        results.append({"query": query, "response": response})
        print("-" * 40)
    
    # Show statistics
    stats = agent.get_knowledge_stats()
    print(f"\n📊 Final Statistics:")
    for key, value in stats.items():
        print(f"   - {key.replace('_', ' ').title()}: {value}")


def main():
    """Main function to run examples"""
    print("🤖 Multi-Folder Agent Examples")
    print("Choose an example to run:")
    print("1. Basic Usage with Predefined Folders")
    print("2. Dynamic File and Folder Attachment")
    print("3. Interactive Session")
    print("4. Batch Processing Multiple Queries")
    print("5. Run All Examples")
    
    try:
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == "1":
            example_basic_usage()
        elif choice == "2":
            example_dynamic_attachment()
        elif choice == "3":
            example_interactive_session()
        elif choice == "4":
            example_batch_processing()
        elif choice == "5":
            print("🚀 Running all examples...")
            example_basic_usage()
            example_dynamic_attachment()
            example_batch_processing()
            print("\n✅ All examples completed!")
            
            # Ask if user wants interactive mode
            interactive_choice = input("\nWould you like to try interactive mode? (y/n): ").strip().lower()
            if interactive_choice in ['y', 'yes']:
                example_interactive_session()
        else:
            print("❌ Invalid choice. Please run the script again.")
    
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Error: {e}")


if __name__ == "__main__":
    main()
