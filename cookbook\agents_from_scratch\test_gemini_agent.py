"""Test script for the Gemini-powered Multi-Folder Agent

This script tests the basic functionality of the Multi-Folder Agent using Gemini models.

Install dependencies: `pip install google-generativeai lancedb tantivy agno`

Before running, make sure you have set up your Google AI API key:
- Get an API key from https://aistudio.google.com/app/apikey
- Set it as an environment variable: export GOOGLE_API_KEY="your-api-key"
- Or set it in your code (not recommended for production)
"""

import os
import tempfile
from pathlib import Path
from multi_folder_agent import MultiFolderAgent


def create_test_documents():
    """Create temporary test documents for testing"""
    # Create a temporary directory
    temp_dir = Path(tempfile.mkdtemp(prefix="gemini_agent_test_"))
    
    # Create sample documents
    (temp_dir / "ai_overview.txt").write_text("""
    Artificial Intelligence Overview
    
    Artificial Intelligence (AI) is a branch of computer science that aims to create 
    intelligent machines that can perform tasks that typically require human intelligence.
    
    Key areas of AI include:
    - Machine Learning: Algorithms that improve through experience
    - Natural Language Processing: Understanding and generating human language
    - Computer Vision: Interpreting and understanding visual information
    - Robotics: Creating intelligent physical agents
    
    AI has applications in healthcare, finance, transportation, and many other fields.
    """)
    
    (temp_dir / "gemini_info.txt").write_text("""
    Google Gemini Information
    
    Gemini is Google's most capable AI model, designed to be multimodal from the ground up.
    It can understand and generate text, code, images, audio, and video.
    
    Key features:
    - Advanced reasoning capabilities
    - Multimodal understanding
    - Code generation and debugging
    - Long context understanding
    - Safety and responsibility built-in
    
    Gemini models are available in different sizes:
    - Gemini Ultra: Most capable model
    - Gemini Pro: Balanced performance and efficiency
    - Gemini Nano: Optimized for on-device tasks
    """)
    
    # Create a JSON file with structured data
    import json
    project_data = {
        "project_name": "Multi-Folder Agent",
        "description": "An AI agent that can work with multiple document sources using Gemini",
        "technologies": ["Python", "Google Gemini", "LanceDB", "Agno Framework"],
        "features": [
            "Multi-folder document loading",
            "Dynamic file attachment",
            "Interactive chat interface",
            "Multiple file format support"
        ],
        "supported_formats": ["PDF", "DOCX", "TXT", "JSON", "CSV"],
        "model_info": {
            "primary_model": "gemini-2.0-flash-001",
            "embedder": "gemini-embedding-exp-03-07",
            "provider": "Google AI"
        }
    }
    
    with open(temp_dir / "project_info.json", "w") as f:
        json.dump(project_data, f, indent=2)
    
    print(f"✅ Test documents created in: {temp_dir}")
    return temp_dir


def test_basic_functionality():
    """Test basic agent functionality"""
    print("\n" + "="*60)
    print("TESTING: Basic Gemini Agent Functionality")
    print("="*60)
    
    # Create test documents
    test_dir = create_test_documents()
    
    try:
        # Initialize the agent
        print("🤖 Initializing Multi-Folder Agent with Gemini...")
        agent = MultiFolderAgent(
            name="Gemini Test Agent",
            initial_folders=[test_dir],
            model_id="gemini-2.0-flash-001",
            embedder_model="gemini-embedding-exp-03-07"
        )
        
        # Load knowledge base
        print("📚 Loading knowledge base...")
        agent.load_initial_knowledge(recreate=True)
        
        # Test basic queries
        test_queries = [
            "What documents do you have access to?",
            "Tell me about artificial intelligence from your knowledge base.",
            "What information do you have about Google Gemini?",
            "What is the Multi-Folder Agent project about?",
            "List the technologies used in this project."
        ]
        
        print("🔍 Testing queries...")
        for i, query in enumerate(test_queries, 1):
            print(f"\n📝 Query {i}: {query}")
            print("🤖 Gemini Response:")
            try:
                response = agent.run(query, stream=False)
                print(response.content if hasattr(response, 'content') else str(response))
            except Exception as e:
                print(f"❌ Error: {e}")
            print("-" * 50)
        
        # Test knowledge base statistics
        print("\n📊 Knowledge Base Statistics:")
        stats = agent.get_knowledge_stats()
        for key, value in stats.items():
            print(f"   - {key.replace('_', ' ').title()}: {value}")
        
        print("\n✅ Basic functionality test completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print("Make sure you have set up your Google AI API key correctly.")
    
    finally:
        # Clean up test directory
        import shutil
        try:
            shutil.rmtree(test_dir)
            print(f"🧹 Cleaned up test directory: {test_dir}")
        except:
            print(f"⚠️  Could not clean up test directory: {test_dir}")


def test_dynamic_attachment():
    """Test dynamic file attachment functionality"""
    print("\n" + "="*60)
    print("TESTING: Dynamic File Attachment with Gemini")
    print("="*60)
    
    # Create initial test documents
    test_dir = create_test_documents()
    
    # Create additional documents for dynamic attachment
    dynamic_dir = Path(tempfile.mkdtemp(prefix="gemini_dynamic_test_"))
    
    (dynamic_dir / "machine_learning.txt").write_text("""
    Machine Learning Fundamentals
    
    Machine Learning is a subset of AI that enables computers to learn and improve
    from experience without being explicitly programmed.
    
    Types of Machine Learning:
    1. Supervised Learning: Learning with labeled examples
    2. Unsupervised Learning: Finding patterns in unlabeled data
    3. Reinforcement Learning: Learning through interaction and feedback
    
    Popular algorithms include:
    - Linear Regression
    - Decision Trees
    - Neural Networks
    - Support Vector Machines
    - Random Forest
    """)
    
    try:
        # Initialize agent with initial folder
        agent = MultiFolderAgent(
            name="Dynamic Test Agent",
            initial_folders=[test_dir],
            model_id="gemini-2.0-flash-001"
        )
        
        # Load initial knowledge
        agent.load_initial_knowledge(recreate=True)
        
        # Test query before dynamic attachment
        print("🔍 Query before dynamic attachment:")
        response = agent.run("What do you know about machine learning?", stream=False)
        print("🤖 Response:", response.content if hasattr(response, 'content') else str(response))
        
        # Dynamically attach new folder
        print(f"\n📁 Dynamically attaching folder: {dynamic_dir}")
        success = agent.attach_folder(dynamic_dir, metadata={"category": "ml", "source": "dynamic"})
        
        if success:
            print("✅ Dynamic attachment successful!")
            
            # Test query after dynamic attachment
            print("\n🔍 Query after dynamic attachment:")
            response = agent.run("Now what do you know about machine learning? Include information from all your sources.", stream=False)
            print("🤖 Response:", response.content if hasattr(response, 'content') else str(response))
        else:
            print("❌ Dynamic attachment failed!")
        
        print("\n✅ Dynamic attachment test completed!")
        
    except Exception as e:
        print(f"❌ Dynamic attachment test failed: {e}")
    
    finally:
        # Clean up
        import shutil
        for dir_path in [test_dir, dynamic_dir]:
            try:
                shutil.rmtree(dir_path)
                print(f"🧹 Cleaned up: {dir_path}")
            except:
                print(f"⚠️  Could not clean up: {dir_path}")


def check_api_key():
    """Check if Google AI API key is set"""
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("⚠️  WARNING: GOOGLE_API_KEY environment variable not set!")
        print("Please set your Google AI API key:")
        print("1. Get an API key from: https://aistudio.google.com/app/apikey")
        print("2. Set it as environment variable: export GOOGLE_API_KEY='your-api-key'")
        print("3. Or set it in your code (not recommended for production)")
        return False
    else:
        print(f"✅ Google AI API key found (ends with: ...{api_key[-4:]})")
        return True


def main():
    """Main test function"""
    print("🧪 Gemini Multi-Folder Agent Test Suite")
    print("="*60)
    
    # Check API key
    if not check_api_key():
        print("\n❌ Cannot proceed without API key. Please set up your Google AI API key first.")
        return
    
    try:
        # Run tests
        test_basic_functionality()
        test_dynamic_attachment()
        
        print("\n🎉 All tests completed!")
        print("\nNext steps:")
        print("1. Try the interactive mode: python multi_folder_agent.py --interactive")
        print("2. Run the examples: python multi_folder_agent_example.py")
        print("3. Customize the agent for your specific use case")
        
    except KeyboardInterrupt:
        print("\n👋 Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")


if __name__ == "__main__":
    main()
