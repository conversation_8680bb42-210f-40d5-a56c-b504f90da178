"""Multi-Folder Agent - An agent that can load multiple local folders and support dynamic file/folder attachment

This agent extends the basic agent_with_knowledge.py to support:
1. Loading multiple local folders at initialization
2. Dynamically attaching new folders or files as prompts
3. Supporting various file formats (PDF, DOCX, TXT, JSON, CSV)
4. Combining multiple knowledge sources

Install dependencies: `pip install openai lancedb tantivy agno`
"""

import os
from pathlib import Path
from textwrap import dedent
from typing import Dict, List, Optional, Union

from agno.agent import Agent
from agno.document import Document
from agno.document.reader.csv_reader import CSVReader
from agno.document.reader.docx_reader import DocxReader
from agno.document.reader.json_reader import J<PERSON>NReader
from agno.document.reader.pdf_reader import PDFReader
from agno.document.reader.text_reader import TextReader
from agno.embedder.openai import OpenAIEmbedder
from agno.knowledge.combined import CombinedKnowledgeBase
from agno.knowledge.csv import CSVKnowledgeBase
from agno.knowledge.docx import DocxKnowledgeBase
from agno.knowledge.json import JSONKnowledgeBase
from agno.knowledge.pdf import PDFKnowledgeBase
from agno.knowledge.text import TextKnowledgeBase
from agno.models.openai import OpenAIChat
from agno.vectordb.lancedb import LanceDb, SearchType


class MultiFolderAgent:
    """An agent that can work with multiple local folders and dynamically attach files/folders"""
    
    def __init__(
        self,
        name: str = "Multi-Folder Agent",
        model_id: str = "gpt-4o",
        initial_folders: Optional[List[Union[str, Path]]] = None,
        db_path: Optional[str] = None,
        embedder_model: str = "text-embedding-3-small"
    ):
        """Initialize the Multi-Folder Agent
        
        Args:
            name: Name of the agent
            model_id: OpenAI model ID to use
            initial_folders: List of folders to load initially
            db_path: Path to store the vector database
            embedder_model: OpenAI embedder model to use
        """
        self.name = name
        self.model_id = model_id
        self.initial_folders = initial_folders or []
        
        # Setup paths
        cwd = Path(__file__).parent
        self.tmp_dir = cwd.joinpath("tmp")
        self.tmp_dir.mkdir(parents=True, exist_ok=True)
        
        # Database path
        self.db_path = db_path or str(self.tmp_dir.joinpath("lancedb"))
        
        # Initialize embedder
        self.embedder = OpenAIEmbedder(id=embedder_model)
        
        # Initialize knowledge sources
        self.knowledge_sources = []
        self.combined_knowledge = None
        
        # File readers for dynamic attachment
        self.readers = {
            '.pdf': PDFReader(),
            '.txt': TextReader(),
            '.docx': DocxReader(),
            '.doc': DocxReader(),
            '.json': JSONReader(),
            '.csv': CSVReader(),
        }
        
        # Initialize the agent
        self._setup_knowledge_base()
        self._create_agent()
    
    def _setup_knowledge_base(self):
        """Setup the combined knowledge base with initial folders"""
        
        # Create knowledge bases for each supported file type
        for i, folder in enumerate(self.initial_folders):
            folder_path = Path(folder)
            if not folder_path.exists():
                print(f"Warning: Folder {folder_path} does not exist, skipping...")
                continue
            
            # Create separate knowledge bases for each file type in each folder
            folder_name = folder_path.name
            
            # PDF Knowledge Base
            pdf_kb = PDFKnowledgeBase(
                path=folder_path,
                vector_db=LanceDb(
                    uri=self.db_path,
                    table_name=f"pdf_docs_{folder_name}_{i}",
                    search_type=SearchType.hybrid,
                    embedder=self.embedder,
                ),
            )
            
            # Text Knowledge Base
            text_kb = TextKnowledgeBase(
                path=folder_path,
                vector_db=LanceDb(
                    uri=self.db_path,
                    table_name=f"text_docs_{folder_name}_{i}",
                    search_type=SearchType.hybrid,
                    embedder=self.embedder,
                ),
            )
            
            # DOCX Knowledge Base
            docx_kb = DocxKnowledgeBase(
                path=folder_path,
                vector_db=LanceDb(
                    uri=self.db_path,
                    table_name=f"docx_docs_{folder_name}_{i}",
                    search_type=SearchType.hybrid,
                    embedder=self.embedder,
                ),
            )
            
            # JSON Knowledge Base
            json_kb = JSONKnowledgeBase(
                path=folder_path,
                vector_db=LanceDb(
                    uri=self.db_path,
                    table_name=f"json_docs_{folder_name}_{i}",
                    search_type=SearchType.hybrid,
                    embedder=self.embedder,
                ),
            )
            
            # CSV Knowledge Base
            csv_kb = CSVKnowledgeBase(
                path=folder_path,
                vector_db=LanceDb(
                    uri=self.db_path,
                    table_name=f"csv_docs_{folder_name}_{i}",
                    search_type=SearchType.hybrid,
                    embedder=self.embedder,
                ),
            )
            
            # Add to knowledge sources
            self.knowledge_sources.extend([pdf_kb, text_kb, docx_kb, json_kb, csv_kb])
        
        # Create combined knowledge base
        self.combined_knowledge = CombinedKnowledgeBase(
            sources=self.knowledge_sources,
            vector_db=LanceDb(
                uri=self.db_path,
                table_name="combined_multi_folder",
                search_type=SearchType.hybrid,
                embedder=self.embedder,
            ),
        )
    
    def _create_agent(self):
        """Create the agent with the combined knowledge base"""
        self.agent = Agent(
            name=self.name,
            model=OpenAIChat(id=self.model_id),
            description=dedent(f"""\
            You are {self.name}, an AI Agent with access to multiple local folders and documents.
            You can search through various file types including PDFs, text files, DOCX files, JSON files, and CSV files.
            You can also dynamically load new files and folders when requested by the user.
            Your goal is to help users find information across their document collections and provide comprehensive answers."""),
            instructions=dedent("""\
            Your mission is to provide comprehensive support by searching through multiple document sources. Follow these steps:

            1. **Analyze the request**
                - Determine if the request requires searching existing knowledge or loading new documents
                - Identify key search terms if searching is needed
                - If the user wants to attach new files or folders, use the appropriate attachment methods

            2. **Search Process**:
                - Use the `search_knowledge_base` tool to find relevant information across all loaded documents
                - Search across different file types and folders as needed
                - Continue searching until you have comprehensive information

            3. **Dynamic Loading**:
                - If the user requests to attach new files or folders, use the attach_file or attach_folder methods
                - Confirm successful loading and provide feedback to the user

            4. **Response Generation**:
                - Provide comprehensive answers based on the searched information
                - Include source information when relevant
                - If information is not found, suggest loading additional documents

            Key capabilities:
            - Multi-folder document search
            - Support for PDF, DOCX, TXT, JSON, and CSV files
            - Dynamic file and folder attachment
            - Cross-document information synthesis"""),
            knowledge=self.combined_knowledge,
            show_tool_calls=True,
            markdown=True,
        )
    
    def load_initial_knowledge(self, recreate: bool = False):
        """Load the initial knowledge base from configured folders"""
        print("Loading initial knowledge base...")
        if self.combined_knowledge:
            self.combined_knowledge.load(recreate=recreate)
        print("Initial knowledge base loaded successfully!")
    
    def attach_file(self, file_path: Union[str, Path], metadata: Optional[Dict] = None) -> bool:
        """Dynamically attach a single file to the knowledge base
        
        Args:
            file_path: Path to the file to attach
            metadata: Optional metadata to associate with the file
            
        Returns:
            bool: True if successful, False otherwise
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            print(f"Error: File {file_path} does not exist")
            return False
        
        file_ext = file_path.suffix.lower()
        if file_ext not in self.readers:
            print(f"Error: Unsupported file type {file_ext}")
            return False
        
        try:
            # Read the document
            reader = self.readers[file_ext]
            documents = reader.read(file=file_path)
            
            # Add metadata if provided
            if metadata:
                for doc in documents:
                    doc.meta.update(metadata)
            
            # Load documents into the knowledge base
            self.combined_knowledge.load_documents(documents, upsert=True)
            print(f"Successfully attached file: {file_path}")
            return True
            
        except Exception as e:
            print(f"Error attaching file {file_path}: {e}")
            return False
    
    def attach_folder(self, folder_path: Union[str, Path], metadata: Optional[Dict] = None) -> bool:
        """Dynamically attach all supported files in a folder to the knowledge base
        
        Args:
            folder_path: Path to the folder to attach
            metadata: Optional metadata to associate with all files in the folder
            
        Returns:
            bool: True if at least one file was successfully attached
        """
        folder_path = Path(folder_path)
        
        if not folder_path.exists() or not folder_path.is_dir():
            print(f"Error: Folder {folder_path} does not exist or is not a directory")
            return False
        
        success_count = 0
        supported_extensions = list(self.readers.keys())
        
        # Find all supported files in the folder (including subdirectories)
        for file_path in folder_path.rglob("*"):
            if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                # Add folder-specific metadata
                file_metadata = metadata.copy() if metadata else {}
                file_metadata.update({
                    'source_folder': str(folder_path),
                    'relative_path': str(file_path.relative_to(folder_path))
                })
                
                if self.attach_file(file_path, file_metadata):
                    success_count += 1
        
        if success_count > 0:
            print(f"Successfully attached {success_count} files from folder: {folder_path}")
            return True
        else:
            print(f"No supported files found in folder: {folder_path}")
            return False
    
    def run(self, message: str, stream: bool = False) -> str:
        """Run the agent with a message"""
        return self.agent.run(message, stream=stream)
    
    def print_response(self, message: str, stream: bool = True):
        """Print the agent's response to a message"""
        self.agent.print_response(message, stream=stream)


    def list_loaded_folders(self) -> List[str]:
        """List all currently loaded folders"""
        return [str(folder) for folder in self.initial_folders]

    def get_knowledge_stats(self) -> Dict:
        """Get statistics about the loaded knowledge base"""
        stats = {
            "total_sources": len(self.knowledge_sources),
            "loaded_folders": self.list_loaded_folders(),
            "supported_formats": list(self.readers.keys()),
        }

        if self.combined_knowledge and self.combined_knowledge.vector_db:
            try:
                stats["total_documents"] = self.combined_knowledge.vector_db.get_count()
            except:
                stats["total_documents"] = "Unknown"

        return stats


def interactive_mode(agent: MultiFolderAgent):
    """Interactive command-line interface for the agent"""
    print(f"\n🤖 Welcome to {agent.name}!")
    print("=" * 50)

    # Show initial stats
    stats = agent.get_knowledge_stats()
    print(f"📊 Knowledge Base Stats:")
    print(f"   - Total sources: {stats['total_sources']}")
    print(f"   - Loaded folders: {len(stats['loaded_folders'])}")
    print(f"   - Supported formats: {', '.join(stats['supported_formats'])}")
    if 'total_documents' in stats:
        print(f"   - Total documents: {stats['total_documents']}")

    print("\n📝 Available commands:")
    print("   - Type your question to search the knowledge base")
    print("   - 'attach file <path>' to attach a single file")
    print("   - 'attach folder <path>' to attach a folder")
    print("   - 'stats' to show knowledge base statistics")
    print("   - 'quit' or 'exit' to exit")
    print("=" * 50)

    while True:
        try:
            user_input = input("\n💬 You: ").strip()

            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break

            elif user_input.lower() == 'stats':
                stats = agent.get_knowledge_stats()
                print("\n📊 Current Knowledge Base Stats:")
                for key, value in stats.items():
                    print(f"   - {key.replace('_', ' ').title()}: {value}")

            elif user_input.lower().startswith('attach file '):
                file_path = user_input[12:].strip()
                print(f"\n📎 Attaching file: {file_path}")
                success = agent.attach_file(file_path)
                if success:
                    print("✅ File attached successfully!")
                else:
                    print("❌ Failed to attach file.")

            elif user_input.lower().startswith('attach folder '):
                folder_path = user_input[14:].strip()
                print(f"\n📁 Attaching folder: {folder_path}")
                success = agent.attach_folder(folder_path)
                if success:
                    print("✅ Folder attached successfully!")
                else:
                    print("❌ Failed to attach folder.")

            elif user_input:
                print(f"\n🤖 {agent.name}:")
                agent.print_response(user_input, stream=True)

        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")


# Example usage and configuration
if __name__ == "__main__":
    import sys

    # Example folder paths - modify these to match your local folders
    example_folders = [
        # "data/documents",  # Uncomment and replace with your actual folder paths
        # "data/pdfs",
        # "data/texts",
    ]

    # Filter out non-existent folders
    existing_folders = [folder for folder in example_folders if Path(folder).exists()]

    if not existing_folders:
        print("⚠️  No existing folders found in the example list.")
        print("Please modify the 'example_folders' list in the script to include your actual folder paths.")
        print("Or use the interactive mode to attach folders dynamically.")

    # Create the multi-folder agent
    multi_agent = MultiFolderAgent(
        name="Multi-Folder Document Assistant",
        initial_folders=existing_folders,
    )

    # Check if user wants to load initial knowledge
    if len(sys.argv) > 1 and sys.argv[1] == "--load":
        print("🔄 Loading initial knowledge base...")
        multi_agent.load_initial_knowledge(recreate=True)
        print("✅ Initial knowledge base loaded!")

    # Check if user wants interactive mode
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_mode(multi_agent)
    else:
        # Default: show usage and run a test query
        print(f"\n🤖 {multi_agent.name} initialized!")
        print("\nUsage:")
        print("  python multi_folder_agent.py --load          # Load initial knowledge base")
        print("  python multi_folder_agent.py --interactive   # Start interactive mode")
        print("\nRunning test query...")

        multi_agent.print_response(
            "Hello! Please introduce yourself and explain your capabilities. "
            "What types of documents can you work with and how can users interact with you?"
        )
