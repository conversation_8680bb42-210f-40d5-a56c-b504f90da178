# Multi-Folder Agent

An enhanced Agno agent that can load multiple local folders and support dynamic attachment of files and folders as prompts. This agent extends the basic `agent_with_knowledge.py` to provide more flexible document management capabilities.

## Features

- 🗂️ **Multi-folder support**: Load documents from multiple local directories at initialization
- 📎 **Dynamic attachment**: Attach new files or folders during runtime
- 📄 **Multiple file formats**: Support for PDF, DOCX, TXT, JSON, and CSV files
- 🔍 **Unified search**: Search across all loaded documents with a single query
- 💬 **Interactive mode**: Command-line interface for easy interaction
- 📊 **Knowledge base statistics**: Track loaded documents and sources
- 🏷️ **Metadata support**: Associate custom metadata with documents

## Installation

Install the required dependencies:

```bash
pip install openai lancedb tantivy agno
```

## Quick Start

### Basic Usage

```python
from multi_folder_agent import MultiFolderAgent

# Initialize agent with multiple folders
agent = MultiFolderAgent(
    name="My Document Assistant",
    initial_folders=["./documents", "./research", "./notes"],
)

# Load the knowledge base
agent.load_initial_knowledge(recreate=True)  # Set to False after first run

# Query the agent
agent.print_response("What documents do you have access to?")
```

### Dynamic File Attachment

```python
# Attach a single file
agent.attach_file("path/to/document.pdf", metadata={"category": "research"})

# Attach an entire folder
agent.attach_folder("path/to/folder", metadata={"project": "new_project"})

# Query the newly attached content
agent.print_response("What's in the newly attached documents?")
```

### Interactive Mode

```python
from multi_folder_agent import interactive_mode

# Start interactive session
interactive_mode(agent)
```

Or run from command line:

```bash
python multi_folder_agent.py --interactive
```

## File Structure

```
cookbook/agents_from_scratch/
├── multi_folder_agent.py              # Main agent implementation
├── multi_folder_agent_example.py      # Comprehensive examples
├── README_multi_folder_agent.md       # This documentation
└── tmp/                               # Generated database files
    └── lancedb/                       # Vector database storage
```

## Supported File Formats

| Format | Extension | Description |
|--------|-----------|-------------|
| PDF | `.pdf` | Portable Document Format files |
| Text | `.txt` | Plain text files |
| Word | `.docx`, `.doc` | Microsoft Word documents |
| JSON | `.json` | JavaScript Object Notation files |
| CSV | `.csv` | Comma-separated values files |

## Command Line Usage

### Load Initial Knowledge Base

```bash
python multi_folder_agent.py --load
```

### Interactive Mode

```bash
python multi_folder_agent.py --interactive
```

### Available Interactive Commands

- **Regular queries**: Type any question to search the knowledge base
- **`attach file <path>`**: Attach a single file
- **`attach folder <path>`**: Attach an entire folder
- **`stats`**: Show knowledge base statistics
- **`quit`** or **`exit`**: Exit the interactive mode

## Examples

### Example 1: Research Assistant

```python
# Create a research-focused agent
research_agent = MultiFolderAgent(
    name="Research Assistant",
    initial_folders=["./papers", "./notes", "./references"],
    model_id="gpt-4o"
)

# Load and query
research_agent.load_initial_knowledge()
research_agent.print_response("Summarize the key findings from the research papers.")
```

### Example 2: Project Documentation Agent

```python
# Create a project documentation agent
doc_agent = MultiFolderAgent(
    name="Project Documentation Agent",
    initial_folders=["./docs", "./specs", "./meeting_notes"],
)

# Dynamically add new project files
doc_agent.attach_folder("./new_requirements", metadata={"phase": "planning"})

# Query project information
doc_agent.print_response("What are the current project requirements?")
```

### Example 3: Personal Knowledge Manager

```python
# Personal knowledge management
personal_agent = MultiFolderAgent(
    name="Personal Knowledge Manager",
    initial_folders=["./personal_docs", "./learning_materials", "./bookmarks"],
)

# Add new learning materials
personal_agent.attach_file("./new_course.pdf", metadata={"type": "course", "subject": "AI"})

# Find information
personal_agent.print_response("What have I learned about machine learning?")
```

## Configuration Options

### MultiFolderAgent Parameters

- **`name`**: Agent name (default: "Multi-Folder Agent")
- **`model_id`**: OpenAI model to use (default: "gpt-4o")
- **`initial_folders`**: List of folders to load at initialization
- **`db_path`**: Path for vector database storage
- **`embedder_model`**: OpenAI embedder model (default: "text-embedding-3-small")

### Metadata Options

When attaching files or folders, you can include metadata:

```python
metadata = {
    "category": "research",
    "priority": "high",
    "project": "ai_development",
    "date": "2024-01-01",
    "author": "John Doe"
}

agent.attach_file("document.pdf", metadata=metadata)
```

## Advanced Usage

### Custom Knowledge Base Configuration

```python
# Create agent with custom configuration
agent = MultiFolderAgent(
    name="Custom Agent",
    initial_folders=["./data"],
    db_path="./custom_db",
    embedder_model="text-embedding-3-large"
)
```

### Batch Processing

```python
# Process multiple queries
queries = [
    "What are the main topics?",
    "Summarize key findings.",
    "List all mentioned technologies."
]

for query in queries:
    response = agent.run(query)
    print(f"Q: {query}")
    print(f"A: {response}")
```

### Knowledge Base Statistics

```python
# Get detailed statistics
stats = agent.get_knowledge_stats()
print(f"Total sources: {stats['total_sources']}")
print(f"Loaded folders: {stats['loaded_folders']}")
print(f"Total documents: {stats['total_documents']}")
```

## Troubleshooting

### Common Issues

1. **"No existing folders found"**: Ensure the folder paths in `initial_folders` exist
2. **"Unsupported file type"**: Check that files have supported extensions
3. **"Failed to attach file"**: Verify file permissions and format

### Performance Tips

1. **First run**: Use `recreate=True` when loading knowledge base for the first time
2. **Subsequent runs**: Use `recreate=False` to avoid reprocessing existing documents
3. **Large folders**: Consider breaking large folders into smaller chunks
4. **Memory usage**: Monitor memory usage with very large document collections

## Contributing

To extend the agent with new file formats:

1. Add the appropriate reader to the `readers` dictionary
2. Create a new knowledge base class if needed
3. Update the `_setup_knowledge_base` method

## License

This project follows the same license as the Agno framework.
